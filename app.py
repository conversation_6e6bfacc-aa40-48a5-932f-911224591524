"""
Main application file for ChatBI-MySQL.
"""

import argparse
import os
import threading
import asyncio

from dotenv import load_dotenv
from flask import Flask
from werkzeug.middleware.proxy_fix import ProxyFix

from src.api import register_routes
from src.services.feishu.client import start_feishu_client
from src.utils.logger import logger
from src.config.concurrency_config import ConcurrencyConfig
from src.services.auth.session_cleanup import start_session_cleanup_service
from src.services.auth.token_refresh_service import start_token_refresh_service
from src.services.scheduler.offline_task_scheduler import start_scheduler
from src.tasks.cleanup_scheduler import start_cleanup_scheduler, stop_cleanup_scheduler
from src.services.startup.interrupted_conversation_recovery import recover_interrupted_conversations

# --- Configuration ---
load_dotenv()
# 打印所有的环境变量
for key, value in os.environ.items():
    logger.info(f"{key}:{value}")

APPLICATION_ROOT = os.getenv("APPLICATION_ROOT", "/crm-chatbi")
ENABLE_BOT_MESSAGE_PROCESSING = (
    os.getenv("ENABLE_BOT_MESSAGE_PROCESSING", "False").lower() == "true"
)
logger.info(f"APPLICATION_ROOT:{APPLICATION_ROOT}")

APP_NAME = "CRM-ChatBI-MySQL"
app = Flask(
    APP_NAME,
    static_folder="src/static",  # Updated path to static folder
    template_folder="src/templates",  # Updated path to templates folder
)
# Apply ProxyFix to handle headers from Nginx
app.wsgi_app = ProxyFix(
    app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1
)

app.secret_key = APP_NAME  # Used for session encryption
MEGABYTE = (2**10) ** 2
app.config["MAX_CONTENT_LENGTH"] = None
app.config["MAX_FORM_MEMORY_SIZE"] = 20 * MEGABYTE

ENABLE_BACKGROUND_TASK = os.getenv("ENABLE_BACKGROUND_TASK", "false").lower() == "true"
logger.info(f"ENABLE_BACKGROUND_TASK:{ENABLE_BACKGROUND_TASK}")

# Register all API routes
register_routes(app)

# --- 初始化服务（无论是直接运行还是通过WSGI导入都需要执行） ---


def initialize_services():
    """初始化所有后台服务"""

    if not ENABLE_BACKGROUND_TASK:
        logger.info("背景任务未启用，跳过初始化")
        return

    # 应用启动时立即处理被中断的对话
    try:
        logger.info("开始处理被中断的对话...")
        recover_interrupted_conversations()
        logger.info("被中断对话处理完成")
    except Exception as e:
        logger.exception(f"处理被中断对话失败: {e}", exc_info=True)

    # 打印并发配置信息
    config_summary = ConcurrencyConfig.get_config_summary()
    logger.info(f"并发配置: {config_summary}")

    # 验证配置
    config_validation = ConcurrencyConfig.validate_config()
    if not config_validation["valid"]:
        logger.exception(f"配置验证失败: {config_validation['issues']}")
    if config_validation["warnings"]:
        logger.warning(f"配置警告: {config_validation['warnings']}")

    # Start Feishu client in a separate thread
    if ENABLE_BOT_MESSAGE_PROCESSING:
        feishu_thread = threading.Thread(target=start_feishu_client, daemon=True)
        feishu_thread.start()
        logger.info("Feishu client WebSocket connection started in background thread")

    # 启动Session清理服务
    start_session_cleanup_service()
    logger.info("Session cleanup service started in background thread")

    # 启动Token刷新服务（专门负责token刷新，避免其他地方重复刷新）
    start_token_refresh_service()
    logger.info("Token refresh service started in background thread")

    # 启动消息清理调度器
    start_cleanup_scheduler()
    logger.info("Message cleanup scheduler started in background thread")

    # 启动离线推荐任务调度器（类似refresh token的异步定时任务）
    def start_offline_system():
        from src.services.recommendation.offline_recommendation_service import OfflineRecommendationService

        logger.info("启动离线推荐系统...")
        try:
            # 启动调度器
            start_scheduler()

            # 立即检查并生成离线推荐（如有需要）
            offline_service = OfflineRecommendationService()
            logger.info("检查是否需要立即生成离线推荐...")
            offline_service.run_daily_recommendation_task()

            logger.info("离线推荐系统启动完成")
        except Exception as e:
            logger.exception(f"启动离线推荐系统失败: {e}", exc_info=True)

    # 启动离线系统并在后台执行
    offline_thread = threading.Thread(target=start_offline_system, daemon=True)
    offline_thread.start()

    # 启动部门Top10常问清单周度分析调度器
    def start_weekly_analysis_system():
        from src.services.scheduler.weekly_top_questions_scheduler import start_weekly_scheduler

        logger.info("启动部门Top10常问清单周度分析调度器...")
        try:
            start_weekly_scheduler()
            logger.info("部门Top10常问清单周度分析调度器启动完成")
        except Exception as e:
            logger.exception(f"启动周度分析调度器失败: {e}", exc_info=True)

    # 启动周度分析系统并在后台执行
    weekly_thread = threading.Thread(target=start_weekly_analysis_system, daemon=True)
    weekly_thread.start()

# 初始化所有服务（无论是直接运行还是通过WSGI导入都会执行）
initialize_services()

# --- Main Application Start ---
if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Run Flask application")

    # Add port parameter, default value is 5700
    parser.add_argument("--port", type=int, default=5700, help="Listen port")

    # Add debug mode parameter, default is True
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument(
        "--no-debug", action="store_false", dest="debug", help="Disable debug mode"
    )
    parser.set_defaults(debug=False)

    # Parse command line arguments
    args = parser.parse_args()

    # Run in development environment for debugging
    app.run(host="0.0.0.0", port=args.port, debug=args.debug)
