# 被中断对话恢复功能

## 功能概述

被中断对话恢复功能用于在应用重启时自动检查并处理被中断的用户查询，避免用户查询因系统重启而丢失。

## 实现原理

### 触发时机
- 应用启动时自动执行（在 `app.py` 的 `initialize_services()` 函数中）

### 检查条件
查找满足以下条件的对话记录：
1. 对话中存在 `is_in_process = 1` 的 `assistant` 消息
2. 消息的 `timestamp` 早于当前应用启动时间

### 处理逻辑
1. **更新消息状态**：将符合条件的 `assistant` 消息的 `is_in_process` 字段更新为 `0`（已完成）
2. **添加恢复提示**：
   - 如果对话的最后一条消息是 `role=assistant`，在其内容末尾追加恢复提示
   - 如果最后一条消息不是 `assistant` 或追加失败，创建新的 `assistant` 消息
3. **恢复消息内容**：`"因系统重启，您的任务已终止，您可回复'请重试'继续和ChatBI对话"`

## 核心组件

### 1. 恢复服务类
- **文件位置**：`src/services/startup/interrupted_conversation_recovery.py`
- **主要类**：`InterruptedConversationRecovery`
- **核心方法**：
  - `find_interrupted_conversations()`: 查找被中断的对话
  - `recover_single_conversation()`: 恢复单个对话
  - `recover_all_interrupted_conversations()`: 批量恢复所有被中断的对话

### 2. API 端点
- **文件位置**：`src/api/recovery_api.py`
- **端点列表**：
  - `GET /api/recovery/stats`: 获取恢复服务统计信息
  - `POST /api/recovery/run`: 手动执行恢复任务
  - `GET /api/recovery/health`: 恢复服务健康检查

### 3. 测试脚本
- **文件位置**：`test_interrupted_recovery.py`
- **功能**：创建测试数据、执行恢复功能、验证恢复结果

## 配置参数

### 应用启动时间
- 自动记录应用启动时的毫秒时间戳
- 用于判断消息是否在应用重启前创建

### 批量处理大小
- 默认值：100
- 控制单次处理的对话数量，避免一次性处理过多数据

### 恢复消息模板
- 默认内容：`"因系统重启，您的任务已终止，您可回复'请重试'继续和ChatBI对话"`
- 可在代码中修改 `recovery_message` 参数

## 使用方法

### 自动执行
应用启动时会自动执行恢复功能，无需手动干预。

### 手动执行
1. **通过 API**：
   ```bash
   curl -X POST http://localhost:5700/api/recovery/run
   ```

2. **通过测试脚本**：
   ```bash
   python test_interrupted_recovery.py
   ```

### 监控和调试
1. **查看统计信息**：
   ```bash
   curl http://localhost:5700/api/recovery/stats
   ```

2. **健康检查**：
   ```bash
   curl http://localhost:5700/api/recovery/health
   ```

## 日志记录

### 日志级别
- **INFO**：正常的恢复操作和统计信息
- **WARNING**：发现大量被中断对话或部分恢复失败
- **ERROR**：恢复操作失败
- **DEBUG**：详细的执行过程信息

### 关键日志示例
```
[INFO] 应用启动时间: 1704067200000 (2024-01-01 00:00:00)
[INFO] 发现 3 个被中断的对话
[INFO] 开始恢复对话: conv_123 (用户: <EMAIL>)
[INFO] 对话 conv_123 恢复成功 (追加到现有消息)
[INFO] 被中断对话恢复完成: 发现3个, 成功恢复3个, 失败0个
```

## 错误处理

### 数据库连接失败
- 记录错误日志
- 返回空结果，不影响应用启动

### 单个对话恢复失败
- 记录错误日志
- 继续处理其他对话
- 在统计信息中记录失败数量

### 批量操作超时
- 使用批量处理限制单次处理数量
- 避免长时间阻塞应用启动

## 性能考虑

### 启动时间影响
- 恢复操作在后台线程中执行，不阻塞应用启动
- 批量处理限制避免一次性处理过多数据

### 数据库性能
- 使用索引优化查询（`is_in_process`, `role`, `timestamp`）
- 批量更新减少数据库交互次数

### 内存使用
- 分批处理避免一次性加载大量数据
- 及时释放不需要的对象引用

## 安全考虑

### 权限控制
- API 端点需要登录认证
- 只有授权用户可以手动执行恢复操作

### 数据完整性
- 使用事务确保数据一致性
- 恢复失败时不影响原有数据

### 并发安全
- 避免多个实例同时执行恢复操作
- 使用应用启动时间作为唯一标识

## 扩展功能

### 定时恢复
可以考虑添加定时任务，定期检查和恢复被中断的对话：
```python
# 在调度器中添加
schedule.every(30).minutes.do(recover_interrupted_conversations)
```

### 恢复策略配置
可以添加配置文件支持不同的恢复策略：
- 恢复消息模板自定义
- 超时时间阈值配置
- 批量处理大小调整

### 通知机制
可以添加通知功能，在发现大量被中断对话时发送告警：
- 邮件通知
- 钉钉/飞书消息
- 系统监控告警
