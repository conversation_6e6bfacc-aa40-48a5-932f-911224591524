"""
被中断对话恢复相关的 API 端点
"""

from flask import jsonify, Blueprint
from src.services.auth.user_login_with_feishu import login_required
from src.services.startup.interrupted_conversation_recovery import (
    recover_interrupted_conversations,
    get_recovery_stats,
    interrupted_conversation_recovery_service
)
from src.utils.logger import logger

# 创建 Blueprint
recovery_bp = Blueprint('recovery', __name__, url_prefix='/api/recovery')


@recovery_bp.route('/stats', methods=['GET'])
@login_required
def get_recovery_service_stats():
    """
    获取恢复服务的统计信息
    
    Returns:
        JSON: 包含恢复服务统计信息的响应
    """
    try:
        stats = get_recovery_stats()
        
        # 添加当前被中断对话的统计
        interrupted_conversations = interrupted_conversation_recovery_service.find_interrupted_conversations()
        stats['current_interrupted_count'] = len(interrupted_conversations)
        stats['current_interrupted_conversations'] = interrupted_conversations
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.exception(f"获取恢复服务统计失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@recovery_bp.route('/run', methods=['POST'])
@login_required
def manual_recovery():
    """
    手动执行被中断对话恢复
    
    Returns:
        JSON: 包含恢复结果的响应
    """
    try:
        logger.info("手动执行被中断对话恢复")
        
        # 执行恢复
        recovery_stats = recover_interrupted_conversations()
        
        return jsonify({
            'success': True,
            'message': '恢复任务执行完成',
            'data': recovery_stats
        })
        
    except Exception as e:
        logger.exception(f"手动执行恢复失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@recovery_bp.route('/health', methods=['GET'])
@login_required
def recovery_health_check():
    """
    恢复服务健康检查
    
    Returns:
        JSON: 包含健康状态的响应
    """
    try:
        # 获取基本统计信息
        stats = get_recovery_stats()
        
        # 检查当前被中断的对话数量
        interrupted_conversations = interrupted_conversation_recovery_service.find_interrupted_conversations()
        interrupted_count = len(interrupted_conversations)
        
        # 判断健康状态
        if interrupted_count == 0:
            status = 'healthy'
            message = '没有发现被中断的对话'
        elif interrupted_count < 5:
            status = 'warning'
            message = f'发现 {interrupted_count} 个被中断的对话'
        else:
            status = 'critical'
            message = f'发现大量被中断的对话: {interrupted_count} 个'
        
        health_info = {
            'status': status,
            'message': message,
            'interrupted_count': interrupted_count,
            'app_start_time': stats['app_start_time_formatted'],
            'recovery_message': stats['recovery_message']
        }
        
        return jsonify({
            'success': True,
            'data': health_info
        })
        
    except Exception as e:
        logger.exception(f"恢复服务健康检查失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
