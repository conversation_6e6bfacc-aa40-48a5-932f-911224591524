"""
被中断对话恢复领域服务

在应用启动时自动检查并处理被中断的用户查询，避免用户查询因系统重启而丢失。
遵循DDD架构模式，复用现有的历史服务和消息清理服务。
"""

import time
from datetime import datetime
from typing import List, Dict
from src.utils.logger import logger
from src.services.chatbot.message_cleanup_service import MessageCleanupService
from src.repositories.chatbi.history import execute_db_query, update_streaming_assistant_message


class InterruptedConversationRecoveryService:
    """
    被中断对话恢复领域服务

    遵循DDD架构模式，负责处理应用重启时被中断对话的恢复业务逻辑。
    复用现有的消息清理服务和历史服务。
    """

    def __init__(self, message_cleanup_service: MessageCleanupService):
        """
        初始化被中断对话恢复服务

        Args:
            message_cleanup_service: 消息清理服务实例
        """
        self.message_cleanup_service = message_cleanup_service

        # 应用启动时间（毫秒时间戳）
        self.app_start_time = int(time.time() * 1000)
        logger.info(f"应用启动时间: {self.app_start_time} ({datetime.fromtimestamp(self.app_start_time/1000)})")

        # 配置参数
        self.batch_size = 100  # 批量处理大小
        self.recovery_message = "因系统重启，您的任务已终止，您可回复'请重试'继续和ChatBI对话"
    
    def find_interrupted_conversations(self) -> List[Dict]:
        """
        查找被中断的对话

        基于消息清理服务的检测逻辑，但使用应用启动时间作为判断标准

        Returns:
            List[Dict]: 被中断的对话列表
        """
        try:
            # 先获取所有被中断的消息（基于应用启动时间）
            interrupted_messages = self._get_interrupted_messages_before_startup()

            if not interrupted_messages:
                logger.info("未发现被中断的消息")
                return []

            # 按对话分组统计
            conversation_stats = {}
            for msg in interrupted_messages:
                conv_key = (msg['conversation_id'], msg['username'], msg['email'])

                if conv_key not in conversation_stats:
                    conversation_stats[conv_key] = {
                        'conversation_id': msg['conversation_id'],
                        'username': msg['username'],
                        'email': msg['email'],
                        'interrupted_message_count': 0,
                        'earliest_interrupted_time': msg['created_at'],
                        'latest_interrupted_time': msg['created_at'],
                        'message_ids': []
                    }

                stats = conversation_stats[conv_key]
                stats['interrupted_message_count'] += 1
                stats['message_ids'].append(msg['id'])

                # 更新时间范围
                if msg['created_at'] < stats['earliest_interrupted_time']:
                    stats['earliest_interrupted_time'] = msg['created_at']
                if msg['created_at'] > stats['latest_interrupted_time']:
                    stats['latest_interrupted_time'] = msg['created_at']

            # 转换为列表并排序
            interrupted_conversations = list(conversation_stats.values())
            interrupted_conversations.sort(key=lambda x: x['latest_interrupted_time'], reverse=True)

            logger.info(f"发现 {len(interrupted_conversations)} 个被中断的对话")
            for conv in interrupted_conversations:
                logger.info(
                    f"被中断对话: ID={conv['conversation_id']}, "
                    f"用户={conv['username']}({conv['email']}), "
                    f"中断消息数={conv['interrupted_message_count']}, "
                    f"最早中断时间={conv['earliest_interrupted_time']}"
                )

            return interrupted_conversations

        except Exception as e:
            logger.exception(f"查找被中断对话失败: {e}", exc_info=True)
            return []

    def _get_interrupted_messages_before_startup(self) -> List[Dict]:
        """
        获取应用启动前被中断的消息

        复用消息清理服务的查询逻辑，但使用应用启动时间作为截止时间

        Returns:
            List[Dict]: 被中断的消息列表
        """
        try:
            # 将应用启动时间转换为datetime对象
            startup_datetime = datetime.fromtimestamp(self.app_start_time / 1000)

            sql = """
                SELECT id, conversation_id, username, email, content,
                       created_at, updated_at, agent
                FROM chat_history
                WHERE is_in_process = 1
                AND role = 'assistant'
                AND created_at < %s
                ORDER BY created_at ASC
                LIMIT %s
            """

            interrupted_messages = execute_db_query(
                sql,
                (startup_datetime, self.batch_size * 10),  # 允许更多消息用于分组
                fetch='all'
            )

            if interrupted_messages:
                logger.info(f"发现 {len(interrupted_messages)} 个被中断的消息")

            return interrupted_messages or []

        except Exception as e:
            logger.exception(f"获取被中断消息失败: {e}", exc_info=True)
            return []
    


    
    def recover_single_conversation(self, conversation_info: Dict) -> bool:
        """
        恢复单个被中断的对话

        复用消息清理服务的清理逻辑，并添加恢复提示消息

        Args:
            conversation_info: 对话信息

        Returns:
            bool: 恢复是否成功
        """
        conversation_id = conversation_info['conversation_id']
        username = conversation_info['username']
        email = conversation_info['email']

        try:
            logger.info(f"开始恢复对话: {conversation_id} (用户: {username})")

            # 1. 从对话信息中获取被中断的消息ID（如果有的话）
            interrupted_message_ids = conversation_info.get('message_ids', [])

            # 如果对话信息中没有消息ID，则查询获取
            if not interrupted_message_ids:
                interrupted_message_ids = self._get_interrupted_message_ids(conversation_id, username, email)

            if not interrupted_message_ids:
                logger.warning(f"对话 {conversation_id} 没有找到被中断的消息")
                return False

            # 2. 复用消息清理服务的清理逻辑来处理每个被中断的消息
            recovery_success_count = 0
            for message_id in interrupted_message_ids:
                success = self.message_cleanup_service.cleanup_stale_message(
                    message_id,
                    "系统重启中断"
                )
                if success:
                    recovery_success_count += 1

            # 3. 在最后一个被恢复的消息中追加特殊的恢复提示
            if recovery_success_count > 0:
                last_message_id = interrupted_message_ids[-1]  # 最后一个消息
                self._append_restart_recovery_message(last_message_id)

                logger.info(f"对话 {conversation_id} 恢复成功: 处理了 {recovery_success_count}/{len(interrupted_message_ids)} 个被中断消息")
                return True
            else:
                logger.error(f"对话 {conversation_id} 恢复失败: 没有成功处理任何被中断消息")
                return False

        except Exception as e:
            logger.exception(f"恢复对话 {conversation_id} 时发生错误: {e}", exc_info=True)
            return False

    def _get_interrupted_message_ids(self, conversation_id: str, username: str, email: str) -> List[int]:
        """
        获取对话中所有被中断的消息ID（备用方法）

        当对话信息中没有预先获取的消息ID时使用

        Args:
            conversation_id: 对话ID
            username: 用户名
            email: 用户邮箱

        Returns:
            List[int]: 被中断的消息ID列表
        """
        try:
            # 复用已有的查询逻辑
            startup_datetime = datetime.fromtimestamp(self.app_start_time / 1000)

            sql = """
                SELECT id
                FROM chat_history
                WHERE conversation_id = %s
                AND username = %s
                AND email = %s
                AND role = 'assistant'
                AND is_in_process = 1
                AND created_at < %s
                ORDER BY created_at ASC
            """

            results = execute_db_query(
                sql,
                (conversation_id, username, email, startup_datetime),
                fetch='all'
            )

            message_ids = [row['id'] for row in results] if results else []
            logger.debug(f"对话 {conversation_id} 找到 {len(message_ids)} 个被中断的消息")

            return message_ids

        except Exception as e:
            logger.exception(f"获取被中断消息ID失败: {e}", exc_info=True)
            return []

    def _append_restart_recovery_message(self, message_id: int) -> bool:
        """
        在指定消息中追加重启恢复提示

        Args:
            message_id: 消息ID

        Returns:
            bool: 是否成功
        """
        try:
            # 获取当前消息内容
            sql = "SELECT content FROM chat_history WHERE id = %s"
            result = execute_db_query(sql, (message_id,), fetch='one')

            if not result:
                logger.warning(f"消息 {message_id} 不存在")
                return False

            current_content = result.get('content', '')

            # 替换通用的中断提示为重启特定的提示
            restart_message = f"\n\n⚠️ **系统提示**: {self.recovery_message}"

            # 如果已经有通用的中断提示，替换它；否则追加
            if "此消息因" in current_content and "而中断" in current_content:
                # 找到并替换现有的中断提示
                import re
                pattern = r'\n\n⚠️ \*\*系统提示\*\*: 此消息因.*?而中断，内容可能不完整。'
                new_content = re.sub(pattern, restart_message, current_content)
            else:
                # 直接追加
                new_content = current_content.rstrip() + restart_message

            # 更新消息内容
            success = update_streaming_assistant_message(
                message_id=message_id,
                content=new_content,
                logs="系统重启恢复：追加重启提示",
                is_completed=True
            )

            if success:
                logger.info(f"成功为消息 {message_id} 追加重启恢复提示")
                return True
            else:
                logger.error(f"为消息 {message_id} 追加重启恢复提示失败")
                return False

        except Exception as e:
            logger.exception(f"追加重启恢复提示失败: {e}", exc_info=True)
            return False
    
    def recover_all_interrupted_conversations(self) -> Dict[str, int]:
        """
        恢复所有被中断的对话
        
        Returns:
            Dict[str, int]: 恢复统计信息
        """
        stats = {
            'found': 0,
            'recovered': 0,
            'failed': 0
        }
        
        try:
            # 查找被中断的对话
            interrupted_conversations = self.find_interrupted_conversations()
            stats['found'] = len(interrupted_conversations)
            
            if stats['found'] == 0:
                logger.info("没有发现被中断的对话")
                return stats
            
            # 逐个恢复对话
            for conversation_info in interrupted_conversations:
                success = self.recover_single_conversation(conversation_info)
                
                if success:
                    stats['recovered'] += 1
                else:
                    stats['failed'] += 1
            
            logger.info(
                f"被中断对话恢复完成: 发现{stats['found']}个, "
                f"成功恢复{stats['recovered']}个, "
                f"失败{stats['failed']}个"
            )
            
            return stats
            
        except Exception as e:
            logger.exception(f"批量恢复被中断对话失败: {e}", exc_info=True)
            return stats


# 全局实例 - 使用依赖注入模式
from src.services.chatbot.message_cleanup_service import message_cleanup_service

interrupted_conversation_recovery_service = InterruptedConversationRecoveryService(
    message_cleanup_service=message_cleanup_service
)


def recover_interrupted_conversations() -> Dict[str, int]:
    """
    恢复被中断的对话（应用启动时调用）

    Returns:
        Dict[str, int]: 恢复统计信息
    """
    logger.info("开始执行被中断对话恢复任务")
    stats = interrupted_conversation_recovery_service.recover_all_interrupted_conversations()
    logger.info(f"被中断对话恢复任务完成: {stats}")
    return stats


def get_recovery_stats() -> Dict[str, any]:
    """
    获取恢复服务统计信息

    Returns:
        Dict[str, any]: 统计信息
    """
    return {
        'app_start_time': interrupted_conversation_recovery_service.app_start_time,
        'app_start_time_formatted': datetime.fromtimestamp(
            interrupted_conversation_recovery_service.app_start_time / 1000
        ).strftime('%Y-%m-%d %H:%M:%S'),
        'recovery_message': interrupted_conversation_recovery_service.recovery_message
    }
