"""
被中断对话恢复领域服务

在应用启动时自动检查并处理被中断的用户查询，避免用户查询因系统重启而丢失。
遵循DDD架构模式，复用现有的历史服务和消息清理服务。
"""

import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from src.utils.logger import logger
from src.services.chatbot.history_service import save_assistant_message
from src.services.chatbot.message_cleanup_service import MessageCleanupService
from src.repositories.chatbi.history import execute_db_query, update_streaming_assistant_message


class InterruptedConversationRecoveryService:
    """
    被中断对话恢复领域服务

    遵循DDD架构模式，负责处理应用重启时被中断对话的恢复业务逻辑。
    复用现有的消息清理服务和历史服务。
    """

    def __init__(self, message_cleanup_service: MessageCleanupService):
        """
        初始化被中断对话恢复服务

        Args:
            message_cleanup_service: 消息清理服务实例
        """
        self.message_cleanup_service = message_cleanup_service

        # 应用启动时间（毫秒时间戳）
        self.app_start_time = int(time.time() * 1000)
        logger.info(f"应用启动时间: {self.app_start_time} ({datetime.fromtimestamp(self.app_start_time/1000)})")

        # 配置参数
        self.batch_size = 100  # 批量处理大小
        self.recovery_message = "因系统重启，您的任务已终止，您可回复'请重试'继续和ChatBI对话"
    
    def find_interrupted_conversations(self) -> List[Dict]:
        """
        查找被中断的对话
        
        查找条件：
        1. 对话中存在 is_in_process = 1 的 assistant 消息
        2. 消息的创建时间早于应用启动时间
        
        Returns:
            List[Dict]: 被中断的对话列表
        """
        try:
            sql = """
                SELECT DISTINCT 
                    ch.conversation_id,
                    ch.username,
                    ch.email,
                    COUNT(*) as interrupted_message_count,
                    MIN(ch.created_at) as earliest_interrupted_time,
                    MAX(ch.created_at) as latest_interrupted_time
                FROM chat_history ch
                WHERE ch.is_in_process = 1 
                AND ch.role = 'assistant'
                AND ch.timestamp < %s
                GROUP BY ch.conversation_id, ch.username, ch.email
                ORDER BY latest_interrupted_time DESC
                LIMIT %s
            """
            
            interrupted_conversations = execute_db_query(
                sql, 
                (self.app_start_time, self.batch_size), 
                fetch='all'
            )
            
            if interrupted_conversations:
                logger.info(f"发现 {len(interrupted_conversations)} 个被中断的对话")
                for conv in interrupted_conversations:
                    logger.info(
                        f"被中断对话: ID={conv['conversation_id']}, "
                        f"用户={conv['username']}({conv['email']}), "
                        f"中断消息数={conv['interrupted_message_count']}, "
                        f"最早中断时间={conv['earliest_interrupted_time']}"
                    )
            else:
                logger.info("未发现被中断的对话")
            
            return interrupted_conversations or []
            
        except Exception as e:
            logger.exception(f"查找被中断对话失败: {e}", exc_info=True)
            return []
    
    def get_conversation_last_assistant_message(self, conversation_id: str, username: str, email: str) -> Optional[Dict]:
        """
        获取对话中最后一条 assistant 消息
        
        Args:
            conversation_id: 对话ID
            username: 用户名
            email: 用户邮箱
            
        Returns:
            Optional[Dict]: 最后一条 assistant 消息，如果不存在则返回 None
        """
        try:
            sql = """
                SELECT id, role, content, timestamp, is_in_process
                FROM chat_history 
                WHERE conversation_id = %s 
                AND username = %s 
                AND email = %s 
                AND role = 'assistant'
                ORDER BY timestamp DESC, id DESC
                LIMIT 1
            """
            
            result = execute_db_query(
                sql, 
                (conversation_id, username, email), 
                fetch='one'
            )
            
            return result
            
        except Exception as e:
            logger.exception(f"获取对话最后一条assistant消息失败: {e}", exc_info=True)
            return None
    
    def update_interrupted_messages_status(self, conversation_id: str, username: str, email: str) -> int:
        """
        将对话中所有被中断的 assistant 消息状态更新为已完成
        
        Args:
            conversation_id: 对话ID
            username: 用户名
            email: 用户邮箱
            
        Returns:
            int: 更新的消息数量
        """
        try:
            sql = """
                UPDATE chat_history 
                SET is_in_process = 0, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE conversation_id = %s 
                AND username = %s 
                AND email = %s 
                AND role = 'assistant'
                AND is_in_process = 1
                AND timestamp < %s
            """
            
            result = execute_db_query(
                sql, 
                (conversation_id, username, email, self.app_start_time), 
                fetch='rowcount'
            )
            
            updated_count = result if result is not None else 0
            logger.info(f"对话 {conversation_id} 更新了 {updated_count} 条被中断消息的状态")
            
            return updated_count
            
        except Exception as e:
            logger.exception(f"更新被中断消息状态失败: {e}", exc_info=True)
            return 0
    
    def append_recovery_message_to_last_assistant(self, message_id: int, current_content: str) -> bool:
        """
        在最后一条 assistant 消息中追加恢复提示
        
        Args:
            message_id: 消息ID
            current_content: 当前消息内容
            
        Returns:
            bool: 是否成功
        """
        try:
            # 在现有内容后追加恢复消息
            new_content = current_content.rstrip()
            if new_content:
                new_content += "\n\n"
            new_content += f"⚠️ **系统提示**: {self.recovery_message}"
            
            sql = """
                UPDATE chat_history 
                SET content = %s, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            
            execute_db_query(sql, (new_content, message_id))
            logger.info(f"成功在消息 {message_id} 中追加恢复提示")
            return True
            
        except Exception as e:
            logger.exception(f"追加恢复消息失败: {e}", exc_info=True)
            return False
    
    def create_new_assistant_recovery_message(self, conversation_id: str, username: str, email: str) -> bool:
        """
        创建新的 assistant 恢复消息
        
        Args:
            conversation_id: 对话ID
            username: 用户名
            email: 用户邮箱
            
        Returns:
            bool: 是否成功
        """
        try:
            content = f"⚠️ **系统提示**: {self.recovery_message}"
            
            # 使用当前时间作为时间戳
            current_timestamp = int(time.time() * 1000)
            
            success = save_message(
                username=username,
                email=email,
                conversation_id=conversation_id,
                role='assistant',
                content=content,
                timestamp=current_timestamp,
                logs="系统自动生成的恢复消息",
                agent='system_recovery'
            )
            
            if success:
                logger.info(f"成功为对话 {conversation_id} 创建恢复消息")
                return True
            else:
                logger.error(f"为对话 {conversation_id} 创建恢复消息失败")
                return False
                
        except Exception as e:
            logger.exception(f"创建恢复消息失败: {e}", exc_info=True)
            return False
    
    def recover_single_conversation(self, conversation_info: Dict) -> bool:
        """
        恢复单个被中断的对话
        
        Args:
            conversation_info: 对话信息
            
        Returns:
            bool: 恢复是否成功
        """
        conversation_id = conversation_info['conversation_id']
        username = conversation_info['username']
        email = conversation_info['email']
        
        try:
            logger.info(f"开始恢复对话: {conversation_id} (用户: {username})")
            
            # 1. 更新所有被中断消息的状态为已完成
            updated_count = self.update_interrupted_messages_status(conversation_id, username, email)
            
            # 2. 获取最后一条 assistant 消息
            last_assistant_msg = self.get_conversation_last_assistant_message(conversation_id, username, email)
            
            if last_assistant_msg and last_assistant_msg['role'] == 'assistant':
                # 3. 在最后一条 assistant 消息中追加恢复提示
                success = self.append_recovery_message_to_last_assistant(
                    last_assistant_msg['id'], 
                    last_assistant_msg.get('content', '')
                )
                
                if success:
                    logger.info(f"对话 {conversation_id} 恢复成功 (追加到现有消息)")
                    return True
                else:
                    logger.warning(f"对话 {conversation_id} 追加恢复消息失败，尝试创建新消息")
            
            # 4. 如果没有 assistant 消息或追加失败，创建新的 assistant 消息
            success = self.create_new_assistant_recovery_message(conversation_id, username, email)
            
            if success:
                logger.info(f"对话 {conversation_id} 恢复成功 (创建新消息)")
                return True
            else:
                logger.error(f"对话 {conversation_id} 恢复失败")
                return False
                
        except Exception as e:
            logger.exception(f"恢复对话 {conversation_id} 时发生错误: {e}", exc_info=True)
            return False
    
    def recover_all_interrupted_conversations(self) -> Dict[str, int]:
        """
        恢复所有被中断的对话
        
        Returns:
            Dict[str, int]: 恢复统计信息
        """
        stats = {
            'found': 0,
            'recovered': 0,
            'failed': 0
        }
        
        try:
            # 查找被中断的对话
            interrupted_conversations = self.find_interrupted_conversations()
            stats['found'] = len(interrupted_conversations)
            
            if stats['found'] == 0:
                logger.info("没有发现被中断的对话")
                return stats
            
            # 逐个恢复对话
            for conversation_info in interrupted_conversations:
                success = self.recover_single_conversation(conversation_info)
                
                if success:
                    stats['recovered'] += 1
                else:
                    stats['failed'] += 1
            
            logger.info(
                f"被中断对话恢复完成: 发现{stats['found']}个, "
                f"成功恢复{stats['recovered']}个, "
                f"失败{stats['failed']}个"
            )
            
            return stats
            
        except Exception as e:
            logger.exception(f"批量恢复被中断对话失败: {e}", exc_info=True)
            return stats


# 全局实例
interrupted_conversation_recovery = InterruptedConversationRecovery()


def recover_interrupted_conversations() -> Dict[str, int]:
    """
    恢复被中断的对话（应用启动时调用）
    
    Returns:
        Dict[str, int]: 恢复统计信息
    """
    logger.info("开始执行被中断对话恢复任务")
    stats = interrupted_conversation_recovery.recover_all_interrupted_conversations()
    logger.info(f"被中断对话恢复任务完成: {stats}")
    return stats


def get_recovery_stats() -> Dict[str, any]:
    """
    获取恢复服务统计信息
    
    Returns:
        Dict[str, any]: 统计信息
    """
    return {
        'app_start_time': interrupted_conversation_recovery.app_start_time,
        'app_start_time_formatted': datetime.fromtimestamp(
            interrupted_conversation_recovery.app_start_time / 1000
        ).strftime('%Y-%m-%d %H:%M:%S'),
        'recovery_message': interrupted_conversation_recovery.recovery_message
    }
