"""
被中断对话仓储层

遵循DDD架构，负责被中断对话相关的数据访问操作。
将数据访问逻辑从领域服务中分离出来，提供清晰的数据访问接口。
"""

from datetime import datetime
from typing import List, Dict, Optional
from src.utils.logger import logger
from src.repositories.chatbi.history import execute_db_query


class InterruptedConversationRepository:
    """
    被中断对话仓储类
    
    遵循DDD仓储模式，封装被中断对话相关的数据访问逻辑
    """
    
    def __init__(self):
        self.batch_size = 100
    
    def find_interrupted_messages_before_time(self, cutoff_datetime: datetime, limit: int = None) -> List[Dict]:
        """
        查找指定时间之前被中断的消息
        
        Args:
            cutoff_datetime: 截止时间
            limit: 查询限制数量
            
        Returns:
            List[Dict]: 被中断的消息列表
        """
        try:
            limit = limit or (self.batch_size * 10)
            
            sql = """
                SELECT id, conversation_id, username, email, content, 
                       created_at, updated_at, agent
                FROM chat_history 
                WHERE is_in_process = 1 
                AND role = 'assistant'
                AND created_at < %s
                ORDER BY created_at ASC
                LIMIT %s
            """
            
            messages = execute_db_query(
                sql, 
                (cutoff_datetime, limit), 
                fetch='all'
            )
            
            if messages:
                logger.debug(f"仓储层查询到 {len(messages)} 个被中断的消息")
            
            return messages or []
            
        except Exception as e:
            logger.exception(f"查询被中断消息失败: {e}", exc_info=True)
            return []
    
    def find_interrupted_messages_by_conversation(self, conversation_id: str, username: str, 
                                                email: str, cutoff_datetime: datetime) -> List[Dict]:
        """
        查找指定对话中被中断的消息
        
        Args:
            conversation_id: 对话ID
            username: 用户名
            email: 用户邮箱
            cutoff_datetime: 截止时间
            
        Returns:
            List[Dict]: 被中断的消息列表
        """
        try:
            sql = """
                SELECT id, content, created_at, updated_at
                FROM chat_history 
                WHERE conversation_id = %s 
                AND username = %s 
                AND email = %s 
                AND role = 'assistant'
                AND is_in_process = 1
                AND created_at < %s
                ORDER BY created_at ASC
            """
            
            messages = execute_db_query(
                sql, 
                (conversation_id, username, email, cutoff_datetime), 
                fetch='all'
            )
            
            if messages:
                logger.debug(f"对话 {conversation_id} 查询到 {len(messages)} 个被中断的消息")
            
            return messages or []
            
        except Exception as e:
            logger.exception(f"查询对话被中断消息失败: {e}", exc_info=True)
            return []
    
    def get_message_content(self, message_id: int) -> Optional[str]:
        """
        获取消息内容
        
        Args:
            message_id: 消息ID
            
        Returns:
            Optional[str]: 消息内容，如果不存在则返回None
        """
        try:
            sql = "SELECT content FROM chat_history WHERE id = %s"
            result = execute_db_query(sql, (message_id,), fetch='one')
            
            if result:
                return result.get('content', '')
            
            return None
            
        except Exception as e:
            logger.exception(f"获取消息内容失败: {e}", exc_info=True)
            return None
    
    def count_interrupted_conversations(self, cutoff_datetime: datetime) -> int:
        """
        统计被中断的对话数量
        
        Args:
            cutoff_datetime: 截止时间
            
        Returns:
            int: 被中断的对话数量
        """
        try:
            sql = """
                SELECT COUNT(DISTINCT conversation_id) as count
                FROM chat_history 
                WHERE is_in_process = 1 
                AND role = 'assistant'
                AND created_at < %s
            """
            
            result = execute_db_query(sql, (cutoff_datetime,), fetch='one')
            
            if result:
                return result.get('count', 0)
            
            return 0
            
        except Exception as e:
            logger.exception(f"统计被中断对话数量失败: {e}", exc_info=True)
            return 0
    
    def get_conversation_summary(self, cutoff_datetime: datetime, limit: int = None) -> List[Dict]:
        """
        获取被中断对话的汇总信息
        
        Args:
            cutoff_datetime: 截止时间
            limit: 查询限制数量
            
        Returns:
            List[Dict]: 对话汇总信息列表
        """
        try:
            limit = limit or self.batch_size
            
            sql = """
                SELECT DISTINCT 
                    ch.conversation_id,
                    ch.username,
                    ch.email,
                    COUNT(*) as interrupted_message_count,
                    MIN(ch.created_at) as earliest_interrupted_time,
                    MAX(ch.created_at) as latest_interrupted_time
                FROM chat_history ch
                WHERE ch.is_in_process = 1 
                AND ch.role = 'assistant'
                AND ch.created_at < %s
                GROUP BY ch.conversation_id, ch.username, ch.email
                ORDER BY latest_interrupted_time DESC
                LIMIT %s
            """
            
            conversations = execute_db_query(
                sql, 
                (cutoff_datetime, limit), 
                fetch='all'
            )
            
            if conversations:
                logger.debug(f"仓储层查询到 {len(conversations)} 个被中断的对话汇总")
            
            return conversations or []
            
        except Exception as e:
            logger.exception(f"获取被中断对话汇总失败: {e}", exc_info=True)
            return []
