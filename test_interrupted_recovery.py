#!/usr/bin/env python3
"""
测试被中断对话恢复功能的脚本

这个脚本可以用来：
1. 创建一些测试数据（模拟被中断的对话）
2. 测试恢复功能
3. 验证恢复结果
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import logger
from src.repositories.chatbi.history import execute_db_query, save_message
from src.services.startup.interrupted_conversation_recovery import recover_interrupted_conversations, get_recovery_stats


def create_test_interrupted_conversation():
    """创建一个测试用的被中断对话"""
    try:
        # 测试数据
        username = "test_user"
        email = "<EMAIL>"
        conversation_id = f"test_conv_{int(time.time())}"
        
        # 创建一个较早的时间戳（模拟应用重启前的消息）
        old_timestamp = int((time.time() - 3600) * 1000)  # 1小时前
        
        logger.info(f"创建测试对话: {conversation_id}")
        
        # 1. 创建用户消息
        success1 = save_message(
            username=username,
            email=email,
            conversation_id=conversation_id,
            role='user',
            content='请帮我分析一下销售数据',
            timestamp=old_timestamp
        )
        
        # 2. 创建一个被中断的assistant消息（is_in_process=1）
        sql = """
            INSERT INTO chat_history (username, email, conversation_id, role, content, logs, timestamp, agent, is_in_process)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        values = (
            username, email, conversation_id, 'assistant', 
            '正在分析您的销售数据，请稍候...', 
            '开始分析销售数据', 
            old_timestamp + 1000, 
            'data_analysis_agent', 
            1  # 设置为处理中状态
        )
        
        execute_db_query(sql, values, commit=True)
        
        if success1:
            logger.info(f"成功创建测试被中断对话: {conversation_id}")
            return conversation_id
        else:
            logger.error("创建测试对话失败")
            return None
            
    except Exception as e:
        logger.exception(f"创建测试数据失败: {e}", exc_info=True)
        return None


def check_interrupted_messages():
    """检查当前数据库中的被中断消息"""
    try:
        sql = """
            SELECT conversation_id, username, email, content, created_at, is_in_process
            FROM chat_history 
            WHERE is_in_process = 1 AND role = 'assistant'
            ORDER BY created_at DESC
            LIMIT 10
        """
        
        results = execute_db_query(sql, (), fetch='all')
        
        if results:
            logger.info(f"发现 {len(results)} 个被中断的消息:")
            for msg in results:
                logger.info(f"  - 对话ID: {msg['conversation_id']}, 用户: {msg['username']}, 创建时间: {msg['created_at']}")
        else:
            logger.info("没有发现被中断的消息")
            
        return results
        
    except Exception as e:
        logger.exception(f"检查被中断消息失败: {e}", exc_info=True)
        return []


def verify_recovery_result(conversation_id: str):
    """验证恢复结果"""
    try:
        sql = """
            SELECT id, role, content, is_in_process, updated_at
            FROM chat_history 
            WHERE conversation_id = %s
            ORDER BY timestamp ASC
        """
        
        results = execute_db_query(sql, (conversation_id,), fetch='all')
        
        if results:
            logger.info(f"对话 {conversation_id} 的恢复结果:")
            for msg in results:
                logger.info(f"  - ID: {msg['id']}, 角色: {msg['role']}, 处理状态: {msg['is_in_process']}")
                logger.info(f"    内容: {msg['content'][:100]}...")
                
            # 检查是否有恢复消息
            recovery_found = any("系统重启" in msg['content'] for msg in results)
            if recovery_found:
                logger.info("✅ 发现恢复消息")
            else:
                logger.warning("❌ 未发现恢复消息")
                
            # 检查是否还有处理中的消息
            processing_count = sum(1 for msg in results if msg['is_in_process'] == 1)
            if processing_count == 0:
                logger.info("✅ 所有消息都已标记为完成")
            else:
                logger.warning(f"❌ 还有 {processing_count} 个消息处于处理中状态")
                
        else:
            logger.warning(f"未找到对话 {conversation_id} 的消息")
            
    except Exception as e:
        logger.exception(f"验证恢复结果失败: {e}", exc_info=True)


def main():
    """主测试函数"""
    logger.info("=== 开始测试被中断对话恢复功能 ===")
    
    # 1. 显示恢复服务统计信息
    stats = get_recovery_stats()
    logger.info(f"恢复服务统计: {stats}")
    
    # 2. 检查当前被中断的消息
    logger.info("\n--- 检查当前被中断的消息 ---")
    interrupted_before = check_interrupted_messages()
    
    # 3. 创建测试数据（如果需要）
    if len(interrupted_before) == 0:
        logger.info("\n--- 创建测试数据 ---")
        test_conversation_id = create_test_interrupted_conversation()
        if test_conversation_id:
            logger.info(f"创建了测试对话: {test_conversation_id}")
            # 再次检查
            interrupted_before = check_interrupted_messages()
        else:
            logger.error("创建测试数据失败")
            return
    
    # 4. 执行恢复功能
    logger.info("\n--- 执行恢复功能 ---")
    recovery_stats = recover_interrupted_conversations()
    logger.info(f"恢复统计: {recovery_stats}")
    
    # 5. 检查恢复后的状态
    logger.info("\n--- 检查恢复后的状态 ---")
    interrupted_after = check_interrupted_messages()
    
    # 6. 验证具体的恢复结果
    if interrupted_before:
        logger.info("\n--- 验证恢复结果 ---")
        for msg in interrupted_before:
            verify_recovery_result(msg['conversation_id'])
    
    logger.info("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
